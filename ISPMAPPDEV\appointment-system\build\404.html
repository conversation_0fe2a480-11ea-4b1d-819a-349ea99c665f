<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>AppointEase - Redirecting</title>
  <script>
    // SPA Redirect Script
    // This handles direct access to routes by redirecting to the homepage
    // with the requested URL path as a parameter
    
    // Get the current path (excluding the /404.html part)
    var path = window.location.pathname;
    
    // Redirect to the index page with the path as a parameter
    window.location.href = '/' + (path.length > 1 ? '?path=' + encodeURIComponent(path) : '');
  </script>
</head>
<body>
  <h1>Redirecting...</h1>
  <p>If you are not redirected automatically, <a href="/">click here</a>.</p>
</body>
</html>
