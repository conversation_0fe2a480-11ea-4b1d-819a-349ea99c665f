## USER SIDE

* **Landing Page**   
  As a user, I want to be able to view the landing page as soon as 5 seconds after I click the link.   
    
* The page should display a Get Started button and redirect the user to the Homepage when clicked once.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.  
    
* **Navigation Bar**   
  As a user, I want to be able to access the other pages and important sections of the website as quickly as possible  
    
* The navigation bar should correctly display the LVCC Logo, system name, and buttons for the pages: Home, About, FAQs, Contact, and Appoint Now  
  * When redirected to the pages Home, About, FAQs, and Contact, the pages on the navigation bar should be underlined in yellow to indicate the page the user is on.  
* The buttons on the navigation bar should be clickable, and are grey in color when hovered. The user only has to click the button once to be redirected to the desired page.  
* The Logo of the navigation bar should be clickable, redirecting the user to the homepage once clicked.  
* The navigation bar should remain at the top of the page even when the user scrolls down.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.

* **Footer**   
  As a user, I want to view the Footer and its contents  
    
* The Footer should display the LVCC Logo and name, and the objective of the LVAppointEase.   
* The footer should display a “Follow Us” section with clickable icons, linking La Verdad Christian College's Facebook and Email Address.  
* The footer should display a “Contact Us” section that includes the LVCC support email address, contact number, and address.  
* The footer should display a copyright, “2024 . La Verdad Christian College, Inc.”

* **Header**  
  As a user, I want to view the header and its contents  
    
* The header should have different contents when redirected to the Announcement Section, How to Appoint Section, and Guidelines Section, which are all under the Homepage.  
* The header should display the correct information, free from grammatical errors, and with appropriate spacing.  
  * Header for Announcement Section: The Header should correctly display the school name and objective of the system  
  * Header for How to Appoint Section: The Header should display the school name and La Verdad Christian College Philosophy.  
  * Header for Guidelines Section: The Header should display the school name and La Verdad Christian College's approach.


* **Home Page**   
  As a user, I want to view the LVAppointEase Homepage and its contents  
    
* The Navigation Bar should be displayed.  
* The Header should correctly display the school name and the objective of the system. With no grammatical errors and with proper spacing.  
* The Announcements, Guidelines, and How to Appoint buttons should be clickable and redirect the user to the desired page when clicked only once.  
* The page should have an arrow-up button on the lower right side of the page. Once clicked, the page scrolls to the top of the page.  
* The footer should be displayed.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.  
* **Announcement Page:**  
  * The Announcements Page Header should be displayed.  
  * The Announcements Page button should be displayed in a white background with a yellow underline.  
  * The Announcements button should have a carousel feature that would change pages once the arrows are clicked.  
    * The first page of the carousel should display  
    * The second page of the carousel should display  
    * The third page of the carousel should display  
  * Registrar Office Hours should display the correct information.  
  * The Registrar Office Appointment section should display a clickable link and mockup of La Verdad Christian College’s Facebook page.  
* **How to Appoint Page:**  
  * The How to Appoint button should redirect the user to the How to Appoint page. The button should be displayed in a white background with a yellow underline.  
  * The How to Appoint header should be displayed.  
  * The How to Appoint Page should display a heading entitled, “Appointment Scheduling” with a subheading, “How to Schedule an Appointment in 5 Easy Steps”.  
  * The How to Appoint Page should display 5 steps, instructing the user how to make an appointment.  
  * The user should be able to click and play the provided video tutorials on how to set an appointment using LVAppointEase  
    * Video Tutorial should be in English   
    * Video Tutorial should be in Tagalog  
* **Guidelines Page:**  
  * The Guidelines button should redirect the user to the Guidelines page. The button should be displayed in a white background with a yellow underline.  
  * The Header for the Guidelines page should be displayed.  
  * The Requesting and Releasing of Documents General Guidelines should be displayed, which includes 7 key guidelines to be followed.  
  * The Conditions and Processing Days of each Document should be displayed.   
    * Each button should be initially displayed by its document name or type, with an arrow pointing downward at the rightmost side.  
    * When clicked, the button should drop down, and the document type or name should be highlighted in white, with an arrow pointing upward at the rightmost side.  
    * The user should be able to click or open multiple buttons.  
    * The dropdown should not be closed unless the button is clicked by the user.

      

* **About Page**  
  As a user, I want to know what La Verdad Christian College is about  
    
* The Navigation Bar should be displayed.  
* The About Page should display 3 sections:  
  * About Us  
  * Vision  
  * Mission  
* The contents of the About Page should be free of grammatical errors and should have proper spacing.  
* The page should have an arrow-up button on the lower right side of the page. Once clicked, the page scrolls all the way to the top of the page.  
* The footer should be displayed.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.


* **FAQs Page**  
  As a user, I want to ask or know the answers to common questions regarding the registrar’s office  
    
* The Navigation Bar should be displayed.  
* Each question should be clickable and should have a “+” symbol on the rightmost side.   
* Questions should be highlighted in white when clicked, showing the answer to the corresponding question by a dropdown.  
  * The user should be able to click or open multiple buttons.  
  * The dropdown should not be closed unless the button is clicked by the user.  
* The page should have an arrow-up button on the lower right side of the page. Once clicked, the page scrolls to the top of the page.  
* The footer should be displayed.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.  
    
* **Contact Page**  
  As a user, I want to be able to contact the registrar’s office when I have any more questions or concerns  
    
* The Navigation Bar should be displayed.  
* The Contact Us header should be displayed correctly. Free of grammatical errors and proper spacing.  
* The page should display a working map location of La Verdad Christian College – Apalit.  
* Users should be able to fill up the form and send a message to the registrar’s office.  
  * The name field should be able to accept alphabetic characters.  
  * The email field should be able to accept alphanumeric characters, requiring “@”.  
  * The subject field should be able to accept alphanumeric characters, up to 100-150 characters only.  
  * The message field should be able to accept alphanumeric characters, up to 200-400 characters only.  
* The Registrar’s Office address, phone number, email address, and Facebook page should be displayed, respectively.  
* The footer should be displayed.  
* The page should be fully responsive, and still visually appealing on different screen sizes.  
* Icons and images should not be pixelated regardless of the screen size.

* **Application Form Page**  
  As a user, I want to be able to book an appointment online  
    
* When the user clicks the Appoint Now button, the user should be redirected to the **Data Privacy Agreement Page**  
  * The Data Privacy Agreement page should have a checkbox for the user to be able to agree to the terms and conditions.  
  * When the checkbox is checked, the user should be able to proceed to the next page.  
  * When the checkbox is unchecked, the user should not be able to proceed to the next page, and the Agreement beside the checkbook should be highlighted in red.  
  * Buttons should be working and are properly spaced.  
    * The back button should redirect the user back to the home page  
    * The next button should redirect the user to the application for records page  
* The Application for Records Page should display:  
  * A progress bar consisting of 4 stages, wherein each stage is highlighted where the user is currently at  
  * **The 1st Page for the Form for Personal Details:**  
    * Surname  
      * The field should have a validation and accept a letter characters only  
    * First Name  
      * The field should have a validation and accept a letter characters only  
    * Middle Name  
      * The field should have a validation and accept a letter characters only  
    * Last School Year Attended  
      * The field should have a validation and accept space, symbols and number characters only.   
      * Limit the allowed characters to 9 characters.   
    * Course/Program/Grade/Strand  
      * The field should accept alphanumeric characters and symbols.  
    * Present Address  
      * The field should accept alphanumeric characters and symbols.  
      * Limit the allowed characters to 100 characters.   
    * Contact Number  
      * The field should only accept numbers as characters only.   
      * Limit the allowed characters to 11 numbers  
    * Email Address  
      * The field should require “@”, accepting alphanumeric characters and symbols  
  * **The 2nd Page for the Document Request**:  
    * The user should be able to select multiple documents for the request.  
    * Selected documents should be displayed right below the selection and consist of a “Remove” button highlighted in red.  
      * When the user clicks the remove button, the document should be unselected in the choices and removed from the selected documents list.  
    * The user should be able to state their purpose or reason for request or application on the “State your Purpose for Applying” field   
      * The field should be able to accept alphanumeric characters, up to 150 to 200 characters.  
    * The user should be able to set the Date of Request  
      * Once clicked, a calendar should appear and the user should be able to select the specific date.  
      * Once the user selects a date, it should be displayed in the field  
    * When the next button is clicked, it should display a pop-up modal regarding the claiming of the requested documents.  
      * The modal should have a clickable selection, either the student or authorizing another person, to claim the document.  
      * When the user chooses the authorization of another person to claim the document selection button, when the next button is clicked, it should redirect the user to the **Uploading of the Necessary Documents or Requirements, the 3rd Page of the Form**  
        * The user should be able to upload a maximum of only 3 files   
        * This field can accept the document types docx, pdf, png, jpeg, and jpg.  
        * Once done, when the user clicks the next button, it should redirect to the calendar page  
      * When the user chooses the “I will claim my document personally” selection button, when the next button is clicked, it should redirect the user to the selection of the preferred date and time of the **Appointment Calendar, the 4th page of the Form.**  
        * The calendar should display the availability and unavailability of the registrar’s office.  
        * Available dates for appointments should be highlighted in green  
          * When a date is selected, the date should be highlighted in light green  
          * Available time slots should be displayed below the calendar, allowing the user to click and choose only one option  
        * Unavailable dates or fully booked appointments should be highlighted in red  
          * When a date is selected, the date should be highlighted in light red  
          * Unavailable or fully booked appointment dates, when clicked, should display a “Fully booked” display text below the calendar  
          * The user should not be able to proceed to the next page when an unavailable date is chosen  
  * Once the user clicks the submit button, a pop-up notification should appear to confirm the submission of the form, displaying the text “Are you sure you want to submit now?”  
    * Once the user clicks the Yes button, the form should automatically save all changes and submit the form  
    * Once the user clicks the Cancel button, the form is not submitted, but all changes made are automatically saved. The user can freely review the form.  
  * Once submitted, a **Feedback or Review Page** should be displayed.  
    * The fields should all require to user to have ratings on each question  
    * Once the user clicks the Done button, the user should be redirected to the end of the form, confirming the completion of the application for records form, and advising the user to wait for the email confirmation regarding the status of their appointment.  
      * A Return to Home button should be displayed, redirecting the user to the Home page once clicked.  
  * Buttons should be working and are properly spaced.  
    * The back button should redirect the user back to the home page only when the user is on the Data Privacy Agreement Page. For further pages, the back button should redirect the user to the previous page of the form  
    * The next button should redirect the user to the next page of the application for records form.  
      * When the user proceeds to the next or even the previous page, the filled-out fields should be saved or retained.

  