@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Regular";
  src: local("Tolkien"), url("/assets/fonts/Tolkien.ttf") format("truetype");
}

@font-face {
  font-family: "Lato-Regular";
  src: local("Lato-Regular"),
    url("/assets/fonts/Lato-Regular.ttf") format("truetype");
}

@font-face {
  font-family: "Lato-Bold";
  src: local("Lato-Bold"), url("/assets/fonts/Lato-Bold.ttf") format("truetype");
}

@font-face {
  font-family: "Lato-Semibold";
  src: local("Lato-Semibold"),
    url("assets/fonts/Lato-Semibold.ttf") format("truetype");
}

@font-face {
  font-family: "Lato-Italic";
  src: local("Lato-Italic"),
    url("/assets/fonts/Lato-Italic.ttf") format("truetype");
}
@font-face {
  font-family: "Lato-Medium";
  src: local("Lato-Medium"),
    url("/assets/fonts/Lato-Medium.ttf") format("truetype");
}

body,
html {
  margin: 0;
  padding: 0;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.overflow-x-auto {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: auto;
}
/* Target scrollbars */
::-webkit-scrollbar {
  width: 8px; /* Adjust scrollbar width */
  height: 8px; /* Adjust scrollbar height (for horizontal scrollbars) */
}

/* Scrollbar track */
::-webkit-scrollbar-track {
  background: #f0f0f0; /* Light gray background */
  border-radius: 4px; /* Rounded corners */
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
  background: #a0aec0; /* Gray thumb */
  border-radius: 4px; /* Rounded corners */
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
  background: #718096; /* Darker gray on hover */
}
input[type="number"].always-show-spinner {
  -moz-appearance: textfield;
}

input[type="number"].always-show-spinner::-webkit-outer-spin-button,
input[type="number"].always-show-spinner::-webkit-inner-spin-button {
  -webkit-appearance: inner-spin-button;
  opacity: 1;
}

input[type="number"].always-show-spinner::-moz-inner-spin-button {
  -moz-appearance: inner-spin-button;
  opacity: 1;
}

.react-tooltip-arrow {
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}

.custom-tooltip {
  background-color: #2d3748 !important;
  color: white !important;

  border: 1px solid rgba(255, 255, 255, 0.1) !important;

  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;

  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
}

@media (prefers-color-scheme: light) {
  .custom-tooltip {
    background-color: #4a5568 !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
  }
}
