{"name": "appointment-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@radix-ui/react-slot": "^1.1.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "firebase": "^11.1.0", "flowbite-react": "^0.10.2", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-calendar": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-multi-date-picker": "^4.5.2", "react-quill": "^2.0.0", "react-router": "^7.0.2", "react-router-dom": "^7.1.1", "react-step-progress-bar": "^1.0.3", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tooltip": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "gh-pages": "^6.2.0", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "vite": "^6.3.5"}}