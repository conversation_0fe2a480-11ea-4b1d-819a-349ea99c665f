{"name": "appointment-system-backend", "version": "1.0.0", "description": "\"# appointment-system-backend\"", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "repository": {"type": "git", "url": "git+https://github.com/Grraffic/appointment-system-backend.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Grraffic/appointment-system-backend/issues"}, "homepage": "https://github.com/Grraffic/appointment-system-backend#readme", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}