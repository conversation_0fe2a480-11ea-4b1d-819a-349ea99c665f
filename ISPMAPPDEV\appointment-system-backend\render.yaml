services:  - type: web
    name: appointment-system-backend
    env: node
    buildCommand: npm install
    startCommand: node server.js
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "10000"
      - key: MONGODB_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: CORS_ORIGINS
        value: http://localhost:5173,https://appointment-system-hy6r.onrender.com,https://appointment-system-backend-1.onrender.com,https://appoinment-system-5687f.web.app,https://appoinment-system-5687f.firebaseapp.com
      - key: GMAIL_USER
        sync: false
      - key: GMAIL_PASS
        sync: false
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOO<PERSON>LE_CLIENT_SECRET
        sync: false
      - key: CLOUDINARY_CLOUD_NAME
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false

  - type: static
    name: appointment-system
    buildCommand: npm run build
    publishDirectory: build
    envVars:
      - key: VITE_API_URL
        value: https://appointment-system-backend.onrender.com
